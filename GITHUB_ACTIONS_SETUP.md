# GitHub Actions Service Account Setup

This document explains how to configure the GitHub Actions service account for automated Cloud Run deployment.

## 🔐 Required Permissions

Your GitHub Actions service account (the one used for `GCP_SA_KEY`) needs these permissions:

### **Minimum Required Permissions:**
```bash
# Cloud Run deployment
roles/run.admin
roles/iam.serviceAccountUser

# Container Registry/Artifact Registry
roles/storage.admin

# Cloud Build (for source-based deployments)
roles/cloudbuild.builds.editor
```

### **Optional IAM Permissions (for automated setup):**
```bash
# To automatically configure service account permissions
roles/resourcemanager.projectIamAdmin
# OR the more specific:
roles/iam.securityAdmin
```

## 🚀 Setup Options

### **Option 1: Fully Automated (Recommended)**

Grant IAM admin permissions to your GitHub Actions service account:

```bash
# For each project (dev, staging, prod)
gcloud projects add-iam-policy-binding PROJECT_ID \
    --member="serviceAccount:YOUR_GITHUB_SA_EMAIL" \
    --role="roles/resourcemanager.projectIamAdmin"
```

**Benefits:**
- ✅ Fully automated deployment
- ✅ Self-healing if permissions get removed
- ✅ No manual setup required

### **Option 2: Manual Setup + Automated Deployment**

1. **One-time manual setup:**
```bash
# Run the setup script once
./scripts/setup-service-accounts.sh
```

2. **Skip IAM setup in CI:**
```yaml
# In GitHub Actions workflows, uncomment:
SKIP_IAM_SETUP: "true"
```

**Benefits:**
- ✅ More secure (CI has minimal permissions)
- ✅ Follows principle of least privilege
- ❌ Requires manual setup

## 🔧 Implementation

The deployment script automatically:

1. **Checks existing permissions** before adding new ones
2. **Safely adds missing permissions** without errors
3. **Skips IAM setup** if `SKIP_IAM_SETUP=true`
4. **Continues deployment** even if IAM operations fail

## 📋 Service Accounts Configured

| Service Account | Purpose | Permissions Added |
|----------------|---------|-------------------|
| `<EMAIL>` | Cloud Run runtime | Secret Manager, Cloud SQL |
| `<EMAIL>` | Container builds | Secret Manager, Cloud SQL |

## 🧪 Testing

```bash
# Test with IAM setup (default)
yarn deploy

# Test without IAM setup
SKIP_IAM_SETUP=true yarn deploy

# Test GitHub Actions
git push origin develop
```

## 🔍 Troubleshooting

### **IAM Permission Errors:**
```
Error: User does not have permission to access project
```

**Solution:** Grant `roles/resourcemanager.projectIamAdmin` to your GitHub Actions service account.

### **Secret Manager Access Denied:**
```
Permission denied to access secret DB_PASSWORD
```

**Solution:** Either:
1. Grant IAM admin permissions to GitHub Actions SA (Option 1)
2. Run `./scripts/setup-service-accounts.sh` manually (Option 2)

### **Cloud Build Failures:**
```
Cloud Build service account does not have access to Secret Manager
```

**Solution:** The deployment script will automatically fix this if it has IAM permissions.

## 🎯 Recommended Approach

**For Production:** Use Option 2 (manual setup + automated deployment)
**For Development:** Use Option 1 (fully automated)

This gives you security in production while maintaining convenience in development.

name: Deploy to Development (Cloud Run)

on:
  push:
    branches:
      - develop
  pull_request_target:
    branches: [ develop ]
    types: [closed]
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to deploy'
        required: true
        default: 'develop'
        type: string

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: development
    if: github.event.pull_request.merged == true || github.event_name == 'push' || github.event_name == 'workflow_dispatch'

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Verify branch exists
        if: github.event_name == 'workflow_dispatch'
        run: |
          BRANCH="${{ github.event.inputs.branch }}"
          if ! git ls-remote --heads origin "$BRANCH" | grep -q "$BRANCH"; then
            echo "::error::Branch '$BRANCH' does not exist in the repository"
            exit 1
          fi

      - name: Checkout selected branch
        if: github.event_name == 'workflow_dispatch'
        run: git checkout ${{ github.event.inputs.branch }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'yarn'

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}
          project_id: ${{ secrets.GCP_PROJECT_ID }}

      - name: Install dependencies
        run: yarn install

      - name: Build
        run: |
          set -x  # Enable verbose output
          yarn run build || {
            echo "Build failed with exit code $?"
            exit 1
          }
        env:
          BUILD_MODE: development
          GCP_PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker to use gcloud as a credential helper
        run: gcloud auth configure-docker

      - name: Make deploy script executable
        run: chmod +x ./scripts/deploy.sh

      - name: Deploy to Cloud Run
        run: yarn deploy
        env:
          BUILD_MODE: development
          GCP_PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
          # Skip IAM setup if service account doesn't have permissions
          # SKIP_IAM_SETUP: "true"
#!/bin/bash

# Cloud Run Migration Testing Script
set -e

echo "🚀 Starting Cloud Run Migration Testing..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
print_status $BLUE "📋 Checking prerequisites..."

if ! command_exists gcloud; then
    print_status $RED "❌ gcloud CLI is not installed"
    exit 1
fi

if ! command_exists docker; then
    print_status $RED "❌ Docker is not installed"
    exit 1
fi

if ! command_exists node; then
    print_status $RED "❌ Node.js is not installed"
    exit 1
fi

if ! command_exists yarn; then
    print_status $RED "❌ Yarn is not installed"
    exit 1
fi

print_status $GREEN "✅ All prerequisites are installed"

# Check if authenticated with gcloud
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    print_status $RED "❌ Not authenticated with gcloud. Please run: gcloud auth login"
    exit 1
fi

print_status $GREEN "✅ Authenticated with gcloud"

# Test Docker build
print_status $BLUE "🐳 Testing Docker build..."
if docker build -t aida-service-test . > /dev/null 2>&1; then
    print_status $GREEN "✅ Docker build successful"
    docker rmi aida-service-test > /dev/null 2>&1
else
    print_status $RED "❌ Docker build failed"
    exit 1
fi

# Test application build
print_status $BLUE "🔨 Testing application build..."
if yarn install --frozen-lockfile > /dev/null 2>&1; then
    print_status $GREEN "✅ Dependencies installed successfully"
else
    print_status $RED "❌ Failed to install dependencies"
    exit 1
fi

# Set test environment
export BUILD_MODE=development
export GCP_PROJECT_ID=${GCP_PROJECT_ID:-"aida-22a9a"}

if yarn build > /dev/null 2>&1; then
    print_status $GREEN "✅ Application build successful"
else
    print_status $RED "❌ Application build failed"
    exit 1
fi

# Test deployment script syntax
print_status $BLUE "📝 Testing deployment script..."
if bash -n scripts/deploy.sh; then
    print_status $GREEN "✅ Deployment script syntax is valid"
else
    print_status $RED "❌ Deployment script has syntax errors"
    exit 1
fi

# Test configuration loading
print_status $BLUE "⚙️  Testing configuration loading..."
if bash -n scripts/cloud-run-config.sh; then
    print_status $GREEN "✅ Configuration script syntax is valid"
else
    print_status $RED "❌ Configuration script has syntax errors"
    exit 1
fi

# Source and test configuration
source scripts/cloud-run-config.sh
if get_cloud_run_config development > /dev/null 2>&1; then
    print_status $GREEN "✅ Configuration loading works"
else
    print_status $RED "❌ Configuration loading failed"
    exit 1
fi

# Test health check endpoint compilation
print_status $BLUE "🏥 Testing health check endpoint..."
if node -e "
const express = require('express');
const app = express();
app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});
console.log('Health check endpoint test passed');
" > /dev/null 2>&1; then
    print_status $GREEN "✅ Health check endpoint test passed"
else
    print_status $RED "❌ Health check endpoint test failed"
    exit 1
fi

print_status $GREEN "🎉 All tests passed! Ready for Cloud Run migration."

echo ""
print_status $YELLOW "📋 Next Steps:"
echo "1. Deploy to development environment first:"
echo "   export BUILD_MODE=development"
echo "   export GCP_PROJECT_ID=aida-22a9a"
echo "   yarn deploy"
echo ""
echo "2. Test the deployed service:"
echo "   curl https://aida-service-dev-PROJECT_NUMBER.us-central1.run.app/api/health"
echo ""
echo "3. If development works, deploy to staging and then production"
echo ""
print_status $BLUE "🔧 Troubleshooting:"
echo "- Check logs: gcloud run logs read --service=aida-service-dev --region=us-central1"
echo "- Monitor metrics in GCP Console: Cloud Run > Services > aida-service-dev"
echo "- Rollback if needed: gcloud run services update-traffic aida-service-dev --to-revisions=PREVIOUS_REVISION=100 --region=us-central1"

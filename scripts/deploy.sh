#!/bin/bash

# Cloud Run deployment script
set -e

# Source the configuration script
source "$(dirname "$0")/cloud-run-config.sh"

# Check required environment variables
if [ -z "$BUILD_MODE" ]; then
    echo "Error: BUILD_MODE environment variable is required"
    exit 1
fi

if [ -z "$GCP_PROJECT_ID" ]; then
    echo "Error: GCP_PROJECT_ID environment variable is required"
    exit 1
fi

# Load environment-specific configurations
get_cloud_run_config $BUILD_MODE
get_cloud_sql_config $BUILD_MODE

# Set variables based on environment
REGION="us-central1"
SERVICE_NAME="aida-service"

case $BUILD_MODE in
    "development")
        SERVICE_NAME="aida-service-dev"
        ;;
    "staging")
        SERVICE_NAME="aida-service-staging"
        ;;
    "production")
        SERVICE_NAME="aida-service-prod"
        ;;
    *)
        echo "Error: Invalid BUILD_MODE. Must be development, staging, or production"
        exit 1
        ;;
esac

echo "Deploying to Cloud Run..."
echo "Environment: $BUILD_MODE"
echo "Project: $GCP_PROJECT_ID"
echo "Service: $SERVICE_NAME"
echo "Region: $REGION"

# Ensure the Cloud Run service account has Secret Manager access
echo "Configuring Secret Manager permissions..."
SERVICE_ACCOUNT_EMAIL="${GCP_PROJECT_ID}-<EMAIL>"
gcloud projects add-iam-policy-binding $GCP_PROJECT_ID \
    --member="serviceAccount:${SERVICE_ACCOUNT_EMAIL}" \
    --role="roles/secretmanager.secretAccessor" \
    --quiet || echo "Warning: Could not set Secret Manager permissions (may already exist)"

# Build and deploy to Cloud Run
gcloud run deploy $SERVICE_NAME \
    --source . \
    --platform managed \
    --region $REGION \
    --project $GCP_PROJECT_ID \
    --allow-unauthenticated \
    --port 8080 \
    --memory $CR_MEMORY \
    --cpu $CR_CPU \
    --min-instances $CR_MIN_INSTANCES \
    --max-instances $CR_MAX_INSTANCES \
    --concurrency $CR_CONCURRENCY \
    --timeout $CR_TIMEOUT \
    --execution-environment gen2 \
    --add-cloudsql-instances $CLOUD_SQL_INSTANCE \
    --set-env-vars "NODE_ENV=$BUILD_MODE,BUILD_MODE=$BUILD_MODE,GCP_PROJECT_ID=$GCP_PROJECT_ID" \
    --verbosity=info

# Get the service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME \
    --platform managed \
    --region $REGION \
    --project $GCP_PROJECT_ID \
    --format 'value(status.url)')

echo "Deployment completed successfully!"
echo "Service URL: $SERVICE_URL"

# Health check
echo "Performing health check..."
sleep 10
if curl -f "$SERVICE_URL/api/health" > /dev/null 2>&1; then
    echo "Health check passed!"
else
    echo "Warning: Health check failed. Please verify the deployment manually."
    exit 1
fi

echo "Deployment verification completed!"
#!/bin/bash

# Cloud Run deployment script
set -e

# Check required environment variables
if [ -z "$BUILD_MODE" ]; then
    echo "Error: BUILD_MODE environment variable is required"
    exit 1
fi

if [ -z "$GCP_PROJECT_ID" ]; then
    echo "Error: GCP_PROJECT_ID environment variable is required"
    exit 1
fi

# Set variables based on environment
REGION="us-central1"
SERVICE_NAME="aida-service"

case $BUILD_MODE in
    "development")
        SERVICE_NAME="aida-service-dev"
        ;;
    "staging")
        SERVICE_NAME="aida-service-staging"
        ;;
    "production")
        SERVICE_NAME="aida-service-prod"
        ;;
    *)
        echo "Error: Invalid BUILD_MODE. Must be development, staging, or production"
        exit 1
        ;;
esac

echo "Deploying to Cloud Run..."
echo "Environment: $BUILD_MODE"
echo "Project: $GCP_PROJECT_ID"
echo "Service: $SERVICE_NAME"
echo "Region: $REGION"

# Build and deploy to Cloud Run
gcloud run deploy $SERVICE_NAME \
    --source . \
    --platform managed \
    --region $REGION \
    --project $GCP_PROJECT_ID \
    --allow-unauthenticated \
    --port 8080 \
    --memory 2Gi \
    --cpu 2 \
    --min-instances 1 \
    --max-instances 100 \
    --concurrency 80 \
    --timeout 300 \
    --set-env-vars "NODE_ENV=$BUILD_MODE" \
    --set-env-vars "BUILD_MODE=$BUILD_MODE" \
    --verbosity=info

# Get the service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME \
    --platform managed \
    --region $REGION \
    --project $GCP_PROJECT_ID \
    --format 'value(status.url)')

echo "Deployment completed successfully!"
echo "Service URL: $SERVICE_URL"

# Health check
echo "Performing health check..."
sleep 10
if curl -f "$SERVICE_URL/api/health" > /dev/null 2>&1; then
    echo "Health check passed!"
else
    echo "Warning: Health check failed. Please verify the deployment manually."
    exit 1
fi

echo "Deployment verification completed!"
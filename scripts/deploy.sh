#!/bin/bash

# Cloud Run deployment script
set -e

# Check required environment variables
if [ -z "$BUILD_MODE" ]; then
    echo "Error: BUILD_MODE environment variable is required"
    exit 1
fi

if [ -z "$GCP_PROJECT_ID" ]; then
    echo "Error: GCP_PROJECT_ID environment variable is required"
    exit 1
fi

# Set environment-specific configurations
get_cloud_run_config() {
    local environment=$1

    case $environment in
        "development")
            export CR_CPU="1"
            export CR_MEMORY="1Gi"
            export CR_MIN_INSTANCES="0"
            export CR_MAX_INSTANCES="10"
            export CLOUD_SQL_INSTANCE="aida-22a9a:europe-west2:aida-db-dev"
            ;;
        "staging")
            export CR_CPU="1"
            export CR_MEMORY="2Gi"
            export CR_MIN_INSTANCES="1"
            export CR_MAX_INSTANCES="20"
            export CLOUD_SQL_INSTANCE="aida---stg:europe-west2:aida-db-stg"
            ;;
        "production")
            export CR_CPU="2"
            export CR_MEMORY="4Gi"
            export CR_MIN_INSTANCES="2"
            export CR_MAX_INSTANCES="100"
            export CLOUD_SQL_INSTANCE="aida-prod-447812:europe-west2:aida-db-prod"
            ;;
        *)
            echo "Error: Invalid BUILD_MODE. Must be development, staging, or production"
            exit 1
            ;;
    esac
}

# Load environment-specific configurations
get_cloud_run_config $BUILD_MODE

# Set variables based on environment
REGION="us-central1"
SERVICE_NAME="aida-service"

case $BUILD_MODE in
    "development")
        SERVICE_NAME="aida-service-dev"
        ;;
    "staging")
        SERVICE_NAME="aida-service-staging"
        ;;
    "production")
        SERVICE_NAME="aida-service-prod"
        ;;
    *)
        echo "Error: Invalid BUILD_MODE. Must be development, staging, or production"
        exit 1
        ;;
esac

echo "Deploying to Cloud Run..."
echo "Environment: $BUILD_MODE"
echo "Project: $GCP_PROJECT_ID"
echo "Service: $SERVICE_NAME"
echo "Region: $REGION"

# Configure service account permissions for Secret Manager access
setup_service_account_permissions() {
    echo "🔐 Configuring service account permissions..."

    # Service accounts
    local COMPUTE_SA_EMAIL="${GCP_PROJECT_ID}-<EMAIL>"
    local CLOUD_BUILD_SA_EMAIL="${GCP_PROJECT_ID}@cloudbuild.gserviceaccount.com"

    # Function to check if a role binding exists
    check_iam_binding() {
        local project=$1
        local member=$2
        local role=$3

        gcloud projects get-iam-policy $project \
            --flatten="bindings[].members" \
            --format="table(bindings.role)" \
            --filter="bindings.members:$member AND bindings.role:$role" \
            --quiet 2>/dev/null | grep -q "$role"
    }

    # Function to safely add IAM binding
    safe_add_iam_binding() {
        local project=$1
        local member=$2
        local role=$3
        local description=$4

        if check_iam_binding "$project" "$member" "$role"; then
            echo "✅ $description (already exists)"
        else
            echo "🔧 Adding $description..."
            if gcloud projects add-iam-policy-binding $project \
                --member="$member" \
                --role="$role" \
                --quiet 2>/dev/null; then
                echo "✅ $description (added successfully)"
            else
                echo "⚠️  Warning: Could not add $description (insufficient permissions or already exists)"
            fi
        fi
    }

    # Setup permissions
    safe_add_iam_binding "$GCP_PROJECT_ID" "serviceAccount:${COMPUTE_SA_EMAIL}" \
        "roles/secretmanager.secretAccessor" "Cloud Run Secret Manager access"

    safe_add_iam_binding "$GCP_PROJECT_ID" "serviceAccount:${CLOUD_BUILD_SA_EMAIL}" \
        "roles/secretmanager.secretAccessor" "Cloud Build Secret Manager access"

    safe_add_iam_binding "$GCP_PROJECT_ID" "serviceAccount:${COMPUTE_SA_EMAIL}" \
        "roles/cloudsql.client" "Cloud Run Cloud SQL access"

    safe_add_iam_binding "$GCP_PROJECT_ID" "serviceAccount:${CLOUD_BUILD_SA_EMAIL}" \
        "roles/cloudsql.client" "Cloud Build Cloud SQL access"
}

# Skip IAM setup if SKIP_IAM_SETUP is set
if [[ "${SKIP_IAM_SETUP}" != "true" ]]; then
    setup_service_account_permissions
else
    echo "⏭️  Skipping IAM setup (SKIP_IAM_SETUP=true)"
fi

# Build and deploy to Cloud Run
gcloud run deploy $SERVICE_NAME \
    --source . \
    --platform managed \
    --region $REGION \
    --project $GCP_PROJECT_ID \
    --allow-unauthenticated \
    --port 8080 \
    --memory $CR_MEMORY \
    --cpu $CR_CPU \
    --min-instances $CR_MIN_INSTANCES \
    --max-instances $CR_MAX_INSTANCES \
    --concurrency 80 \
    --timeout 300 \
    --execution-environment gen2 \
    --add-cloudsql-instances $CLOUD_SQL_INSTANCE \
    --set-env-vars "NODE_ENV=$BUILD_MODE,BUILD_MODE=$BUILD_MODE,GCP_PROJECT_ID=$GCP_PROJECT_ID" \
    --verbosity=info

# Get the service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME \
    --platform managed \
    --region $REGION \
    --project $GCP_PROJECT_ID \
    --format 'value(status.url)')

echo "Deployment completed successfully!"
echo "Service URL: $SERVICE_URL"

# Health check
echo "Performing health check..."
sleep 10
if curl -f "$SERVICE_URL/api/health" > /dev/null 2>&1; then
    echo "Health check passed!"
else
    echo "Warning: Health check failed. Please verify the deployment manually."
    exit 1
fi

echo "Deployment verification completed!"
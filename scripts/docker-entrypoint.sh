#!/bin/bash

# Docker Entrypoint Script for Cloud Run
# This script runs loadenv.js to fetch secrets and create .env file
# before starting the Node.js application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_status $BLUE "🚀 Starting Aida Service Container..."

# Debug environment variables
print_status $YELLOW "Debug: Environment variables:"
echo "  K_SERVICE: ${K_SERVICE:-'not set'}"
echo "  BUILD_MODE: ${BUILD_MODE:-'not set'}"
echo "  GCP_PROJECT_ID: ${GCP_PROJECT_ID:-'not set'}"
echo "  PORT: ${PORT:-'not set'}"

# Check if we're running in Cloud Run or Cloud Build
if [[ -n "${K_SERVICE}" ]]; then
    print_status $BLUE "☁️  Running in Cloud Run environment"
    ENVIRONMENT="cloud-run"
elif [[ -n "${CLOUD_BUILD_PROJECT_ID}" || -n "${BUILD_ID}" ]]; then
    print_status $BLUE "🏗️  Running in Cloud Build environment"
    ENVIRONMENT="cloud-build"
else
    print_status $BLUE "🏠 Running in local environment"
    ENVIRONMENT="local"
fi

# Validate required environment variables
if [[ -z "${BUILD_MODE}" ]]; then
    print_status $RED "❌ BUILD_MODE environment variable is required"
    exit 1
fi

if [[ -z "${GCP_PROJECT_ID}" ]]; then
    print_status $RED "❌ GCP_PROJECT_ID environment variable is required"
    exit 1
fi

print_status $YELLOW "Environment: ${BUILD_MODE}"
print_status $YELLOW "Project: ${GCP_PROJECT_ID}"

# Check if .env file already exists (for local development)
if [[ -f ".env" && "${ENVIRONMENT}" == "local" ]]; then
    print_status $GREEN "✅ .env file already exists, skipping secret loading"
else
    # Load environment configuration and secrets
    print_status $BLUE "📦 Loading environment configuration and secrets..."

    # Export environment variables for loadenv.js
    export BUILD_MODE
    export GCP_PROJECT_ID

    # Check authentication status
    if [[ "${ENVIRONMENT}" == "cloud-run" || "${ENVIRONMENT}" == "cloud-build" ]]; then
        print_status $BLUE "🔐 Using service account authentication..."

        # Verify we can access the metadata server (indicates proper SA setup)
        if curl -s -f -H "Metadata-Flavor: Google" "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/email" > /dev/null 2>&1; then
            print_status $GREEN "✅ Service account authentication available"
        else
            print_status $YELLOW "⚠️  Service account metadata not accessible, but continuing..."
        fi
    fi

    # Run loadenv.js to fetch secrets and create .env file
    if node scripts/loadenv.js; then
        print_status $GREEN "✅ Environment configuration loaded successfully"
    else
        print_status $RED "❌ Failed to load environment configuration"

        # In Cloud environments, this is critical - exit if we can't load secrets
        if [[ "${ENVIRONMENT}" == "cloud-run" || "${ENVIRONMENT}" == "cloud-build" ]]; then
            print_status $RED "💥 Cannot start without environment configuration in cloud environment"
            print_status $YELLOW "🔧 Troubleshooting tips:"
            echo "  1. Check if service account has 'Secret Manager Secret Accessor' role"
            echo "  2. Verify secrets exist in Secret Manager for project: ${GCP_PROJECT_ID}"
            echo "  3. Check Cloud Run service account permissions"
            exit 1
        else
            print_status $YELLOW "⚠️  Continuing without secret loading (local development)"
        fi
    fi
fi

# Verify .env file exists
if [[ ! -f ".env" ]]; then
    print_status $RED "❌ .env file not found after loading"
    exit 1
fi

print_status $GREEN "✅ Environment setup completed"

# Start the Node.js application
print_status $BLUE "🎯 Starting Node.js application..."

# Verify dist directory exists
if [[ ! -d "dist" ]]; then
    print_status $RED "❌ dist directory not found - build may have failed"
    ls -la
    exit 1
fi

# Verify main file exists
if [[ ! -f "dist/index.js" ]]; then
    print_status $RED "❌ dist/index.js not found - build may have failed"
    ls -la dist/
    exit 1
fi

print_status $GREEN "✅ Application files verified"

# Check if we should start in development or production mode
if [[ "${BUILD_MODE}" == "development" && "${ENVIRONMENT}" == "local" ]]; then
    print_status $BLUE "🔧 Starting in development mode..."
    exec yarn dev
else
    print_status $BLUE "🚀 Starting in production mode..."
    print_status $YELLOW "Environment: ${BUILD_MODE}"
    print_status $YELLOW "Port: 8080"
    exec node dist/index.js
fi

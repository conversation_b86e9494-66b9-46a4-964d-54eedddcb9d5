#!/bin/bash

# Local Development Environment Setup
# This script sets up the environment for local development by fetching secrets
# and creating a .env file. For Cloud Run, secrets are loaded at runtime.

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_status $BLUE "🔧 Setting up local development environment..."

# Check if BUILD_MODE is set, default to development
BUILD_MODE=${BUILD_MODE:-development}
print_status $YELLOW "Environment: $BUILD_MODE"

# Check if gcloud is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    print_status $RED "❌ Not authenticated with gcloud"
    print_status $YELLOW "Please run: gcloud auth application-default login"
    exit 1
fi

# Set project based on environment
case $BUILD_MODE in
    "development")
        export GCP_PROJECT_ID="aida-22a9a"
        ;;
    "staging")
        export GCP_PROJECT_ID="aida---stg"
        ;;
    "production")
        export GCP_PROJECT_ID="aida-prod-447812"
        ;;
    *)
        print_status $RED "❌ Invalid BUILD_MODE: $BUILD_MODE"
        print_status $YELLOW "Valid options: development, staging, production"
        exit 1
        ;;
esac

print_status $GREEN "✅ Using project: $GCP_PROJECT_ID"

# Run the original loadenv.js script for local development
print_status $BLUE "📦 Loading environment configuration and secrets..."
export BUILD_MODE
export GCP_PROJECT_ID

if node scripts/loadenv.js; then
    print_status $GREEN "✅ Environment setup completed successfully!"
    print_status $BLUE "📋 Next steps:"
    echo "  1. Start development server: yarn dev"
    echo "  2. Or build and start: yarn build && yarn start"
else
    print_status $RED "❌ Failed to load environment configuration"
    print_status $YELLOW "💡 Troubleshooting:"
    echo "  1. Check gcloud authentication: gcloud auth list"
    echo "  2. Verify project access: gcloud projects describe $GCP_PROJECT_ID"
    echo "  3. Check Secret Manager permissions"
    exit 1
fi

print_status $YELLOW "📝 Note: This .env file is for local development only."
print_status $YELLOW "In Cloud Run, secrets are loaded at runtime from Secret Manager."

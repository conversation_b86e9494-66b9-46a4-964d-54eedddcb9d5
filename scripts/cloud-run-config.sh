#!/bin/bash

# Cloud Run configuration settings for different environments
set -e

get_cloud_run_config() {
    local environment=$1
    
    case $environment in
        "development")
            # Development environment - smaller resources for cost optimization
            export CR_CPU="1"
            export CR_MEMORY="1Gi"
            export CR_MIN_INSTANCES="0"
            export CR_MAX_INSTANCES="10"
            export CR_CONCURRENCY="80"
            export CR_TIMEOUT="300"
            export CR_MAX_SURGE="2"
            export CR_MAX_UNAVAILABLE="0"
            ;;
        "staging")
            # Staging environment - moderate resources for testing
            export CR_CPU="1"
            export CR_MEMORY="2Gi"
            export CR_MIN_INSTANCES="1"
            export CR_MAX_INSTANCES="20"
            export CR_CONCURRENCY="80"
            export CR_TIMEOUT="300"
            export CR_MAX_SURGE="3"
            export CR_MAX_UNAVAILABLE="0"
            ;;
        "production")
            # Production environment - optimized for performance and reliability
            export CR_CPU="2"
            export CR_MEMORY="4Gi"
            export CR_MIN_INSTANCES="2"
            export CR_MAX_INSTANCES="100"
            export CR_CONCURRENCY="80"
            export CR_TIMEOUT="300"
            export CR_MAX_SURGE="5"
            export CR_MAX_UNAVAILABLE="1"
            ;;
        *)
            echo "Error: Invalid environment. Must be development, staging, or production"
            exit 1
            ;;
    esac
    
    echo "Cloud Run configuration for $environment:"
    echo "  CPU: $CR_CPU"
    echo "  Memory: $CR_MEMORY"
    echo "  Min Instances: $CR_MIN_INSTANCES"
    echo "  Max Instances: $CR_MAX_INSTANCES"
    echo "  Concurrency: $CR_CONCURRENCY"
    echo "  Timeout: ${CR_TIMEOUT}s"
    echo "  Max Surge: $CR_MAX_SURGE"
    echo "  Max Unavailable: $CR_MAX_UNAVAILABLE"
}

get_cloud_sql_config() {
    local environment=$1
    
    case $environment in
        "development")
            export CLOUD_SQL_INSTANCE="aida-22a9a:europe-west2:aida-db-dev"
            ;;
        "staging")
            export CLOUD_SQL_INSTANCE="aida---stg:europe-west2:aida-db-stg"
            ;;
        "production")
            export CLOUD_SQL_INSTANCE="aida-prod-447812:europe-west2:aida-db-prod"
            ;;
        *)
            echo "Error: Invalid environment for Cloud SQL configuration"
            exit 1
            ;;
    esac
    
    echo "Cloud SQL Instance: $CLOUD_SQL_INSTANCE"
}

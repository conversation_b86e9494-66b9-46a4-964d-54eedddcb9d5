#!/bin/bash

# Rollback Script for Cloud Run Migration
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_status $RED "🔄 Cloud Run Migration Rollback"
echo "=================================="

# Check if backup file exists
if [[ ! -f .migration-backup ]]; then
    print_status $RED "❌ No migration backup found (.migration-backup file missing)"
    print_status $YELLOW "Cannot perform automatic rollback"
    exit 1
fi

# Load backup information
source .migration-backup

print_status $BLUE "📋 Rollback Information:"
echo "  • Rollback Version: ${ROLLBACK_VERSION:-'Not found'}"
echo "  • Project: ${ROLLBACK_PROJECT:-'Not found'}"
echo "  • Backup Timestamp: ${ROLLBACK_TIMESTAMP:-'Not found'}"
echo "  • Migration Success: ${MIGRATION_SUCCESS:-'false'}"

# Confirmation
read -p "Are you sure you want to rollback to App Engine? (yes/no): " confirm
if [[ $confirm != "yes" ]]; then
    print_status $YELLOW "Rollback cancelled by user"
    exit 0
fi

# Check if we have the necessary information
if [[ -z $ROLLBACK_VERSION || -z $ROLLBACK_PROJECT ]]; then
    print_status $RED "❌ Missing rollback information"
    print_status $YELLOW "Manual rollback required:"
    echo "1. List App Engine versions: gcloud app versions list --project=$ROLLBACK_PROJECT"
    echo "2. Set traffic to previous version: gcloud app services set-traffic default --splits=VERSION_ID=100 --project=$ROLLBACK_PROJECT"
    exit 1
fi

print_status $BLUE "🔄 Starting rollback process..."

# Rollback to App Engine
print_status $YELLOW "Rolling back to App Engine version: $ROLLBACK_VERSION"
if gcloud app services set-traffic default --splits="$ROLLBACK_VERSION=100" --project="$ROLLBACK_PROJECT" --quiet; then
    print_status $GREEN "✅ Successfully rolled back to App Engine"
else
    print_status $RED "❌ Failed to rollback to App Engine"
    exit 1
fi

# Verify App Engine is working
print_status $BLUE "🔍 Verifying App Engine rollback..."
sleep 30

# Construct App Engine URL
case $ROLLBACK_PROJECT in
    "aida-22a9a")
        APP_ENGINE_URL="https://aida-22a9a.nw.r.appspot.com"
        ;;
    "aida---stg")
        APP_ENGINE_URL="https://aida---stg.nw.r.appspot.com"
        ;;
    "aida-prod-447812")
        APP_ENGINE_URL="https://aida-prod-447812.nw.r.appspot.com"
        ;;
    *)
        APP_ENGINE_URL="https://$ROLLBACK_PROJECT.appspot.com"
        ;;
esac

# Health check App Engine
HEALTH_CHECK_ATTEMPTS=0
MAX_HEALTH_ATTEMPTS=10
while [[ $HEALTH_CHECK_ATTEMPTS -lt $MAX_HEALTH_ATTEMPTS ]]; do
    # Try both health endpoints (new and potential existing)
    HEALTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_ENGINE_URL/api/health" 2>/dev/null || echo "000")
    if [[ $HEALTH_STATUS != "200" ]]; then
        # Try root endpoint if health endpoint doesn't exist
        HEALTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$APP_ENGINE_URL/" 2>/dev/null || echo "000")
    fi
    
    if [[ $HEALTH_STATUS =~ ^[23] ]]; then
        print_status $GREEN "✅ App Engine is responding (HTTP $HEALTH_STATUS)"
        break
    else
        print_status $YELLOW "⏳ Health check attempt $((HEALTH_CHECK_ATTEMPTS + 1))/$MAX_HEALTH_ATTEMPTS (HTTP $HEALTH_STATUS)"
        sleep 10
        ((HEALTH_CHECK_ATTEMPTS++))
    fi
done

if [[ $HEALTH_CHECK_ATTEMPTS -eq $MAX_HEALTH_ATTEMPTS ]]; then
    print_status $RED "❌ App Engine health checks failed after rollback"
    print_status $YELLOW "Manual intervention required"
    exit 1
fi

# Optional: Scale down Cloud Run to save costs
read -p "Scale down Cloud Run service to zero to save costs? (yes/no): " scale_down
if [[ $scale_down == "yes" ]]; then
    print_status $BLUE "📉 Scaling down Cloud Run service..."
    
    # Scale down each environment
    for env in dev staging prod; do
        SERVICE_NAME="aida-service-$env"
        if [[ $env == "dev" ]]; then
            PROJECT="aida-22a9a"
        elif [[ $env == "staging" ]]; then
            PROJECT="aida---stg"
        else
            PROJECT="aida-prod-447812"
        fi
        
        if gcloud run services update $SERVICE_NAME --min-instances=0 --max-instances=1 --region=us-central1 --project=$PROJECT --quiet 2>/dev/null; then
            print_status $GREEN "✅ Scaled down $SERVICE_NAME"
        else
            print_status $YELLOW "⚠️  Could not scale down $SERVICE_NAME (may not exist)"
        fi
    done
fi

# Update backup file
echo "ROLLBACK_COMPLETED=$(date -u +%Y%m%d_%H%M%S)" >> .migration-backup
echo "ROLLBACK_SUCCESS=true" >> .migration-backup

print_status $GREEN "🎉 Rollback completed successfully!"
print_status $BLUE "📊 Rollback Summary:"
echo "  • Rolled back to: App Engine version $ROLLBACK_VERSION"
echo "  • Service URL: $APP_ENGINE_URL"
echo "  • Status: Active and responding"

print_status $YELLOW "📋 Post-Rollback Tasks:"
echo "1. Verify all functionality is working correctly"
echo "2. Update any external services if needed"
echo "3. Monitor the service for stability"
echo "4. Investigate and fix the issues that caused the rollback"

print_status $BLUE "🔧 Useful Commands:"
echo "  • Check App Engine logs: gcloud app logs tail --project=$ROLLBACK_PROJECT"
echo "  • List versions: gcloud app versions list --project=$ROLLBACK_PROJECT"
echo "  • Monitor traffic: gcloud app services describe default --project=$ROLLBACK_PROJECT"

print_status $GREEN "✅ Rollback process completed!"

#!/bin/bash

# Production Migration Script for Cloud Run
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_status $BLUE "🚀 Starting Production Migration to Cloud Run"
echo "=================================================="

# Confirmation prompt
read -p "Are you sure you want to proceed with production migration? (yes/no): " confirm
if [[ $confirm != "yes" ]]; then
    print_status $YELLOW "Migration cancelled by user"
    exit 0
fi

# Set production environment
export BUILD_MODE=production
export GCP_PROJECT_ID=aida-prod-447812

print_status $BLUE "📋 Pre-migration Checklist"

# Check if development and staging are working
print_status $YELLOW "Checking development environment..."
DEV_HEALTH=$(curl -s -o /dev/null -w "%{http_code}" https://aida-service-dev-PROJECT_NUMBER.us-central1.run.app/api/health || echo "000")
if [[ $DEV_HEALTH == "200" ]]; then
    print_status $GREEN "✅ Development environment is healthy"
else
    print_status $RED "❌ Development environment is not responding (HTTP $DEV_HEALTH)"
    read -p "Continue anyway? (yes/no): " continue_dev
    if [[ $continue_dev != "yes" ]]; then
        exit 1
    fi
fi

print_status $YELLOW "Checking staging environment..."
STAGING_HEALTH=$(curl -s -o /dev/null -w "%{http_code}" https://aida-service-staging-PROJECT_NUMBER.us-central1.run.app/api/health || echo "000")
if [[ $STAGING_HEALTH == "200" ]]; then
    print_status $GREEN "✅ Staging environment is healthy"
else
    print_status $RED "❌ Staging environment is not responding (HTTP $STAGING_HEALTH)"
    read -p "Continue anyway? (yes/no): " continue_staging
    if [[ $continue_staging != "yes" ]]; then
        exit 1
    fi
fi

# Backup current App Engine version info
print_status $BLUE "📦 Backing up current App Engine configuration..."
CURRENT_VERSION=$(gcloud app versions list --service=default --format="value(version.id)" --filter="traffic_split>0" --project=$GCP_PROJECT_ID | head -1)
if [[ -n $CURRENT_VERSION ]]; then
    print_status $GREEN "✅ Current App Engine version: $CURRENT_VERSION"
    echo "ROLLBACK_VERSION=$CURRENT_VERSION" > .migration-backup
    echo "ROLLBACK_PROJECT=$GCP_PROJECT_ID" >> .migration-backup
    echo "ROLLBACK_TIMESTAMP=$(date -u +%Y%m%d_%H%M%S)" >> .migration-backup
else
    print_status $YELLOW "⚠️  No active App Engine version found"
fi

# Deploy to Cloud Run
print_status $BLUE "🚀 Deploying to Cloud Run Production..."
if yarn deploy; then
    print_status $GREEN "✅ Cloud Run deployment successful"
else
    print_status $RED "❌ Cloud Run deployment failed"
    print_status $YELLOW "Check logs and try again"
    exit 1
fi

# Get Cloud Run service URL
SERVICE_URL=$(gcloud run services describe aida-service-prod \
    --platform managed \
    --region us-central1 \
    --project $GCP_PROJECT_ID \
    --format 'value(status.url)')

print_status $BLUE "🔍 Verifying Cloud Run deployment..."
sleep 30  # Wait for service to be ready

# Health check
HEALTH_CHECK_ATTEMPTS=0
MAX_HEALTH_ATTEMPTS=10
while [[ $HEALTH_CHECK_ATTEMPTS -lt $MAX_HEALTH_ATTEMPTS ]]; do
    HEALTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$SERVICE_URL/api/health" || echo "000")
    if [[ $HEALTH_STATUS == "200" ]]; then
        print_status $GREEN "✅ Cloud Run service is healthy"
        break
    else
        print_status $YELLOW "⏳ Health check attempt $((HEALTH_CHECK_ATTEMPTS + 1))/$MAX_HEALTH_ATTEMPTS (HTTP $HEALTH_STATUS)"
        sleep 10
        ((HEALTH_CHECK_ATTEMPTS++))
    fi
done

if [[ $HEALTH_CHECK_ATTEMPTS -eq $MAX_HEALTH_ATTEMPTS ]]; then
    print_status $RED "❌ Health checks failed after $MAX_HEALTH_ATTEMPTS attempts"
    print_status $YELLOW "🔄 Initiating automatic rollback..."
    
    if [[ -f .migration-backup && -n $CURRENT_VERSION ]]; then
        gcloud app services set-traffic default --splits="$CURRENT_VERSION=100" --project=$GCP_PROJECT_ID
        print_status $GREEN "✅ Rolled back to App Engine version $CURRENT_VERSION"
    fi
    exit 1
fi

# Test critical endpoints
print_status $BLUE "🧪 Testing critical endpoints..."

# Test API endpoints
ENDPOINTS=(
    "/api/health"
    "/api/user/statistics"
    "/api/projects"
)

for endpoint in "${ENDPOINTS[@]}"; do
    STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$SERVICE_URL$endpoint" || echo "000")
    if [[ $STATUS =~ ^[23] ]]; then
        print_status $GREEN "✅ $endpoint responding (HTTP $STATUS)"
    else
        print_status $YELLOW "⚠️  $endpoint returned HTTP $STATUS (may require authentication)"
    fi
done

# Performance test
print_status $BLUE "⚡ Running basic performance test..."
RESPONSE_TIME=$(curl -s -o /dev/null -w "%{time_total}" "$SERVICE_URL/api/health")
print_status $GREEN "✅ Response time: ${RESPONSE_TIME}s"

# Final confirmation
print_status $GREEN "🎉 Migration to Cloud Run completed successfully!"
print_status $BLUE "📊 Migration Summary:"
echo "  • Service URL: $SERVICE_URL"
echo "  • Health Status: Healthy"
echo "  • Response Time: ${RESPONSE_TIME}s"
echo "  • Backup Info: Saved in .migration-backup"

print_status $YELLOW "📋 Post-Migration Tasks:"
echo "1. Update DNS records if using custom domains"
echo "2. Update any external services pointing to the old URL"
echo "3. Monitor the service for the next 24 hours"
echo "4. Clean up App Engine resources after verification"

print_status $BLUE "🔧 Monitoring Commands:"
echo "  • Logs: gcloud run logs read --service=aida-service-prod --region=us-central1"
echo "  • Metrics: Check Cloud Run console"
echo "  • Rollback: ./scripts/rollback-migration.sh"

# Save successful migration info
echo "MIGRATION_SUCCESS=true" >> .migration-backup
echo "CLOUD_RUN_URL=$SERVICE_URL" >> .migration-backup
echo "MIGRATION_COMPLETED=$(date -u +%Y%m%d_%H%M%S)" >> .migration-backup

print_status $GREEN "✅ Production migration completed successfully!"

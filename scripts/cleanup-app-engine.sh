#!/bin/bash

# App Engine Cleanup Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_status $BLUE "🧹 App Engine Cleanup Script"
echo "============================="

# Warning message
print_status $RED "⚠️  WARNING: This script will clean up App Engine resources"
print_status $YELLOW "Make sure Cloud Run migration is successful and stable before proceeding"
echo ""

# Check if migration was successful
if [[ -f .migration-backup ]]; then
    source .migration-backup
    if [[ $MIGRATION_SUCCESS == "true" ]]; then
        print_status $GREEN "✅ Migration backup indicates successful Cloud Run deployment"
    else
        print_status $RED "❌ Migration backup indicates unsuccessful deployment"
        print_status $YELLOW "Cleanup not recommended until migration is successful"
        exit 1
    fi
else
    print_status $YELLOW "⚠️  No migration backup found"
    read -p "Are you sure Cloud Run migration is successful? (yes/no): " confirm_migration
    if [[ $confirm_migration != "yes" ]]; then
        print_status $YELLOW "Cleanup cancelled"
        exit 0
    fi
fi

# Final confirmation
print_status $YELLOW "This will:"
echo "  • Remove old App Engine versions (keeping the latest as backup)"
echo "  • Clean up app.yaml configuration file"
echo "  • Update documentation"
echo ""
read -p "Proceed with cleanup? (yes/no): " confirm_cleanup
if [[ $confirm_cleanup != "yes" ]]; then
    print_status $YELLOW "Cleanup cancelled by user"
    exit 0
fi

# Projects to clean up
PROJECTS=("aida-22a9a" "aida---stg" "aida-prod-447812")
PROJECT_NAMES=("Development" "Staging" "Production")

for i in "${!PROJECTS[@]}"; do
    PROJECT=${PROJECTS[$i]}
    PROJECT_NAME=${PROJECT_NAMES[$i]}
    
    print_status $BLUE "🧹 Cleaning up $PROJECT_NAME environment ($PROJECT)..."
    
    # List current versions
    print_status $YELLOW "Current App Engine versions in $PROJECT:"
    gcloud app versions list --project=$PROJECT --format="table(version.id,traffic_split,version.createTime)" || {
        print_status $YELLOW "⚠️  No App Engine versions found in $PROJECT"
        continue
    }
    
    # Get versions with 0% traffic (safe to delete)
    VERSIONS_TO_DELETE=$(gcloud app versions list --project=$PROJECT --format="value(version.id)" --filter="traffic_split=0" 2>/dev/null | head -10)
    
    if [[ -n $VERSIONS_TO_DELETE ]]; then
        print_status $YELLOW "Versions to delete (0% traffic):"
        echo "$VERSIONS_TO_DELETE"
        
        read -p "Delete these versions from $PROJECT_NAME? (yes/no): " confirm_delete
        if [[ $confirm_delete == "yes" ]]; then
            echo "$VERSIONS_TO_DELETE" | while read -r version; do
                if [[ -n $version ]]; then
                    print_status $BLUE "Deleting version: $version"
                    if gcloud app versions delete "$version" --project=$PROJECT --quiet; then
                        print_status $GREEN "✅ Deleted version $version"
                    else
                        print_status $RED "❌ Failed to delete version $version"
                    fi
                fi
            done
        else
            print_status $YELLOW "Skipped version deletion for $PROJECT_NAME"
        fi
    else
        print_status $GREEN "✅ No unused versions to delete in $PROJECT_NAME"
    fi
    
    echo ""
done

# Archive app.yaml
print_status $BLUE "📦 Archiving App Engine configuration..."
if [[ -f app.yaml ]]; then
    ARCHIVE_NAME="app.yaml.backup.$(date +%Y%m%d_%H%M%S)"
    mv app.yaml "$ARCHIVE_NAME"
    print_status $GREEN "✅ Archived app.yaml as $ARCHIVE_NAME"
else
    print_status $YELLOW "⚠️  app.yaml not found"
fi

# Update .gitignore to exclude App Engine files
print_status $BLUE "📝 Updating .gitignore..."
if [[ -f .gitignore ]]; then
    if ! grep -q "# App Engine" .gitignore; then
        echo "" >> .gitignore
        echo "# App Engine (archived after Cloud Run migration)" >> .gitignore
        echo "app.yaml.backup.*" >> .gitignore
        print_status $GREEN "✅ Updated .gitignore"
    else
        print_status $YELLOW "⚠️  .gitignore already contains App Engine entries"
    fi
else
    print_status $YELLOW "⚠️  .gitignore not found"
fi

# Update README
print_status $BLUE "📚 Updating documentation..."
if [[ -f README.md ]]; then
    # Create backup
    cp README.md README.md.backup
    
    # Add Cloud Run section
    cat >> README.md << 'EOF'

## Cloud Run Deployment

This service has been migrated from App Engine to Cloud Run for better performance and cost optimization.

### Quick Start

```bash
# Test the migration
./scripts/test-migration.sh

# Deploy to development
export BUILD_MODE=development
export GCP_PROJECT_ID=aida-22a9a
yarn deploy

# Deploy to production
./scripts/production-migration.sh
```

### Monitoring

```bash
# Check service status
gcloud run services list --region=us-central1

# View logs
gcloud run logs read --service=aida-service-prod --region=us-central1

# Monitor metrics
# Visit Cloud Run console in GCP
```

### Rollback

If issues occur, you can rollback to App Engine:

```bash
./scripts/rollback-migration.sh
```

For detailed migration information, see [CLOUD_RUN_MIGRATION.md](CLOUD_RUN_MIGRATION.md).
EOF

    print_status $GREEN "✅ Updated README.md with Cloud Run information"
else
    print_status $YELLOW "⚠️  README.md not found"
fi

# Create cleanup summary
print_status $BLUE "📊 Creating cleanup summary..."
cat > .cleanup-summary << EOF
App Engine Cleanup Summary
==========================
Cleanup Date: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
Migration Status: ${MIGRATION_SUCCESS:-"Unknown"}
Cloud Run URL: ${CLOUD_RUN_URL:-"Unknown"}

Actions Taken:
- Cleaned up unused App Engine versions
- Archived app.yaml configuration
- Updated .gitignore
- Updated README.md with Cloud Run information

Backup Files:
- app.yaml.backup.* (archived App Engine configuration)
- README.md.backup (original README)
- .migration-backup (migration information)

Next Steps:
1. Monitor Cloud Run services for stability
2. Update any external documentation
3. Consider removing App Engine from GCP billing if no longer needed
4. Update CI/CD documentation for team members
EOF

print_status $GREEN "✅ Created cleanup summary in .cleanup-summary"

# Final status
print_status $GREEN "🎉 App Engine cleanup completed successfully!"
print_status $BLUE "📋 Summary:"
echo "  • Cleaned up unused App Engine versions"
echo "  • Archived configuration files"
echo "  • Updated documentation"
echo "  • Created cleanup summary"

print_status $YELLOW "📋 Recommended Next Steps:"
echo "1. Monitor Cloud Run services for 24-48 hours"
echo "2. Update team documentation and runbooks"
echo "3. Consider disabling App Engine APIs if no longer needed"
echo "4. Review and optimize Cloud Run costs"
echo "5. Set up monitoring and alerting for Cloud Run"

print_status $BLUE "🔗 Useful Links:"
echo "  • Cloud Run Console: https://console.cloud.google.com/run"
echo "  • Migration Guide: ./CLOUD_RUN_MIGRATION.md"
echo "  • Cleanup Summary: ./.cleanup-summary"

print_status $GREEN "✅ Migration to Cloud Run is now complete!"

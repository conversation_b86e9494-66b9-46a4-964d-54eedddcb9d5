import { SecretManagerServiceClient } from "@google-cloud/secret-manager";
import { log } from "./logger";

// List of secret keys to fetch from GCP Secret Manager
const SECRET_KEYS = [
  "GOOGLE_GENERATIVE_AI_API_KEY",
  "DB_PASSWORD", 
  "REV_AI_TOKEN",
  "SENDGRID_API_KEY",
  "SPEECHMATICS_API_KEY",
] as const;

type SecretKey = typeof SECRET_KEYS[number];

class SecretManager {
  private client: SecretManagerServiceClient;
  private cache: Map<string, string> = new Map();
  private projectId: string;
  private isCloudRun: boolean;

  constructor() {
    this.projectId = process.env.GCP_PROJECT_ID || "";
    this.isCloudRun = process.env.K_SERVICE !== undefined;
    
    if (!this.projectId) {
      throw new Error("GCP_PROJECT_ID environment variable is required");
    }

    try {
      this.client = new SecretManagerServiceClient();
      log.info("Secret Manager client initialized successfully");
    } catch (error) {
      log.error("Failed to initialize Secret Manager client", { error });
      throw error;
    }
  }

  /**
   * Fetch a secret from GCP Secret Manager
   */
  async getSecret(secretName: SecretKey): Promise<string> {
    // Check cache first
    const cacheKey = `${this.projectId}:${secretName}`;
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    try {
      const [version] = await this.client.accessSecretVersion({
        name: `projects/${this.projectId}/secrets/${secretName}/versions/latest`,
      });

      const secretValue = version.payload?.data?.toString();
      if (!secretValue) {
        throw new Error(`Empty secret value for ${secretName}`);
      }

      // Cache the secret for this runtime instance
      this.cache.set(cacheKey, secretValue);
      log.info(`Successfully fetched secret: ${secretName}`);
      
      return secretValue;
    } catch (error: any) {
      if (error.code === 5) {
        log.error(`Secret ${secretName} not found in project ${this.projectId}`);
      } else if (error.code === 7) {
        log.error(`Permission denied to access secret ${secretName}`);
      } else {
        log.error(`Error fetching secret ${secretName}`, { error: error.message });
      }
      throw error;
    }
  }

  /**
   * Fetch all required secrets and set them as environment variables
   */
  async loadAllSecrets(): Promise<void> {
    log.info("Loading secrets from GCP Secret Manager...");
    
    const secretPromises = SECRET_KEYS.map(async (secretName) => {
      try {
        const secretValue = await this.getSecret(secretName);
        process.env[secretName] = secretValue;
        return { secretName, success: true };
      } catch (error) {
        log.warn(`Failed to load secret ${secretName}, continuing without it`, { error });
        return { secretName, success: false };
      }
    });

    const results = await Promise.all(secretPromises);
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;

    log.info(`Secrets loaded: ${successful} successful, ${failed} failed`);
    
    if (failed > 0) {
      const failedSecrets = results.filter(r => !r.success).map(r => r.secretName);
      log.warn("Some secrets failed to load", { failedSecrets });
    }
  }

  /**
   * Get a secret with fallback to environment variable
   */
  async getSecretWithFallback(secretName: SecretKey): Promise<string | undefined> {
    // First try environment variable (for local development)
    const envValue = process.env[secretName];
    if (envValue) {
      return envValue;
    }

    // Then try Secret Manager (for Cloud Run)
    if (this.isCloudRun) {
      try {
        return await this.getSecret(secretName);
      } catch (error) {
        log.warn(`Failed to fetch secret ${secretName} from Secret Manager`, { error });
      }
    }

    return undefined;
  }

  /**
   * Clear the secret cache (useful for testing)
   */
  clearCache(): void {
    this.cache.clear();
    log.info("Secret cache cleared");
  }
}

// Export singleton instance
export const secretManager = new SecretManager();

// Export helper function for easy access
export const getSecret = (secretName: SecretKey): Promise<string | undefined> => {
  return secretManager.getSecretWithFallback(secretName);
};

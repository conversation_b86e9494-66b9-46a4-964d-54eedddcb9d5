import cors from "cors";
import express from "express";
import "express-async-errors";

import apiRoutes from "@/api";
import { db } from "@/schemas";
import { log } from "@/services/logger";
import posthog from "@/services/posthog";
import { secretManager } from "@/services/secrets";
import expressErrorHandler from "./handlers/expressErrorHandler";
import { initializeFirebase } from "./services/firebase";
import { initPubSub } from "./services/pubsub";
import { SchedulerService } from "./services/scheduler";

const app = express();

const PORT = process.env.PORT ?? 8080;

app.use(cors());
app.use(express.json({ limit: "50mb" }));
app.use(express.urlencoded({ limit: "50mb", extended: true }));
app.use("/api", apiRoutes);

app.use(expressErrorHandler);

const main = async () => {
  // Load secrets from GCP Secret Manager (Cloud Run) or use env vars (local)
  const isCloudRun = process.env.K_SERVICE !== undefined;
  if (isCloudRun) {
    log.info("Running in Cloud Run, loading secrets from Secret Manager...");
    try {
      await secretManager.loadAllSecrets();
    } catch (error) {
      log.error(
        "Failed to load secrets, continuing with environment variables",
        { error }
      );
    }
  } else {
    log.info("Running locally, using environment variables");
  }

  await Promise.all([
    initializeFirebase(), // initialize firebase
    db.authenticate(), // check db authentication
  ]);

  // Sync DB
  // TODO: Remove "alter" option later; keep it here for now for faster development due to many changes with schema
  await db.sync({
    alter: true,
  });

  // Subscribe to PubSub messages
  await initPubSub();

  // Initialize scheduler
  // const schedulerService = new SchedulerService();
  // schedulerService.start();

  // Start server
  const server = app.listen(PORT, () => {
    console.info(`Successfully started the server on PORT: ${PORT}`);
    console.info(`Environment variables: ${JSON.stringify(process.env)}`);
    // Capture server start event
    posthog.capture({
      distinctId: "server",
      event: "server_started",
      properties: {
        port: PORT,
        environment: process.env.NODE_ENV || "development",
      },
    });
  });

  // Graceful shutdown handler for Cloud Run
  const gracefulShutdown = async (signal: string) => {
    console.info(`Received ${signal}. Starting graceful shutdown...`);

    // Stop accepting new requests
    server.close(async () => {
      console.info("HTTP server closed");

      try {
        // Close database connections
        await db.close();
        console.info("Database connections closed");

        // Capture shutdown event
        posthog.capture({
          distinctId: "server",
          event: "server_shutdown",
          properties: {
            signal,
            environment: process.env.NODE_ENV || "development",
          },
        });

        console.info("Graceful shutdown completed");
        process.exit(0);
      } catch (error) {
        console.error("Error during graceful shutdown:", error);
        process.exit(1);
      }
    });

    // Force shutdown after 30 seconds
    setTimeout(() => {
      console.error("Forced shutdown after timeout");
      process.exit(1);
    }, 30000);
  };

  // Handle shutdown signals
  process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
  process.on("SIGINT", () => gracefulShutdown("SIGINT"));
};

main().catch((err) => {
  log.stack(err, "fatal");
  // Capture fatal error event
  posthog.capture({
    distinctId: "server",
    event: "fatal_error",
    properties: {
      error: err.message,
      stack: err.stack,
    },
  });
});

# Cloud Run Migration Guide

This document outlines the migration from Google App Engine to Google Cloud Run for the Aida Service.

## 🎯 Migration Overview

### Benefits of Cloud Run

- **Better Performance**: Faster cold starts and improved scaling
- **Cost Optimization**: Pay only for actual usage with better pricing model
- **More Flexibility**: Custom container configurations, GPU support, volume mounts
- **Modern Architecture**: Container-based deployment with better DevOps practices
- **Advanced Features**: Better traffic management, gradual rollouts, and monitoring

### What Changed

- **Containerization**: Application now runs in Docker containers
- **Deployment Method**: Using `gcloud run deploy` instead of `gcloud app deploy`
- **Configuration**: Environment-specific resource allocation
- **Health Checks**: Added `/api/health` endpoint for Cloud Run monitoring
- **Database Connections**: Optimized connection pooling for Cloud Run
- **Graceful Shutdown**: Added proper signal handling for container lifecycle

## 📋 Prerequisites

Before starting the migration, ensure you have:

1. **Tools Installed**:

   - Google Cloud SDK (`gcloud`)
   - Docker
   - Node.js 20+
   - Yarn

2. **Permissions**:

   - Cloud Run Admin
   - Cloud SQL Client
   - Service Account User
   - Storage Admin (for container images)

3. **Secrets**:
   - `GCP_SA_KEY` secret in GitHub Actions (already configured)
   - All environment variables in Secret Manager

## 🚀 Migration Steps

### Step 1: Test the Migration

```bash
# Make the test script executable
chmod +x scripts/test-migration.sh

# Run the test script
./scripts/test-migration.sh
```

### Local Development Setup

For local development, you still need to set up environment variables:

```bash
# Set up local environment (fetches secrets for development)
./scripts/setup-local-env.sh

# Or manually for specific environment
export BUILD_MODE=development
./scripts/setup-local-env.sh
```

### Step 2: Deploy to Development

```bash
# Set environment variables
export BUILD_MODE=development
export GCP_PROJECT_ID=aida-22a9a

# Deploy to Cloud Run
yarn deploy
```

### Step 3: Verify Development Deployment

```bash
# Check service status
gcloud run services describe aida-service-dev --region=us-central1

# Test health endpoint
curl https://aida-service-dev-PROJECT_NUMBER.us-central1.run.app/api/health

# Check logs
gcloud run logs read --service=aida-service-dev --region=us-central1
```

### Step 4: Deploy to Staging

```bash
export BUILD_MODE=staging
export GCP_PROJECT_ID=aida---stg
yarn deploy
```

### Step 5: Deploy to Production

```bash
export BUILD_MODE=production
export GCP_PROJECT_ID=aida-prod-447812
yarn deploy
```

## ⚙️ Configuration Details

### Resource Allocation by Environment

| Environment | CPU | Memory | Min Instances | Max Instances | Concurrency |
| ----------- | --- | ------ | ------------- | ------------- | ----------- |
| Development | 1   | 1Gi    | 0             | 10            | 80          |
| Staging     | 1   | 2Gi    | 1             | 20            | 80          |
| Production  | 2   | 4Gi    | 2             | 100           | 80          |

### Database Connections

- **Connection Pooling**: Optimized for Cloud Run with smaller pool sizes
- **Cloud SQL Integration**: Using `--add-cloudsql-instances` flag
- **Graceful Shutdown**: Proper connection cleanup on container termination

### Health Checks

- **Endpoint**: `/api/health`
- **Startup Probe**: 10s initial delay, 10s period, 30 failure threshold
- **Liveness Probe**: 30s initial delay, 30s period, 3 failure threshold
- **Readiness Probe**: 5s initial delay, 10s period, 3 failure threshold

## 🔧 Troubleshooting

### Common Issues

1. **Cold Start Timeouts**

   - Increase startup probe timeout
   - Optimize application startup time
   - Consider increasing min instances

2. **Database Connection Issues**

   - Verify Cloud SQL instance names in deployment script
   - Check IAM permissions for Cloud SQL
   - Monitor connection pool metrics

3. **Memory Issues**
   - Monitor memory usage in Cloud Run metrics
   - Adjust memory allocation in configuration
   - Check for memory leaks in application

### Monitoring Commands

```bash
# View service details
gcloud run services describe SERVICE_NAME --region=us-central1

# Check logs
gcloud run logs read --service=SERVICE_NAME --region=us-central1

# Monitor metrics
gcloud run services list --region=us-central1

# Update traffic allocation
gcloud run services update-traffic SERVICE_NAME --to-revisions=REVISION=100 --region=us-central1
```

### Rollback Procedure

If issues occur, you can rollback to a previous revision:

```bash
# List revisions
gcloud run revisions list --service=SERVICE_NAME --region=us-central1

# Rollback to previous revision
gcloud run services update-traffic SERVICE_NAME --to-revisions=PREVIOUS_REVISION=100 --region=us-central1
```

## 📊 Post-Migration Checklist

- [ ] All environments deployed successfully
- [ ] Health checks passing
- [ ] Database connections working
- [ ] API endpoints responding correctly
- [ ] Monitoring and alerting configured
- [ ] Performance metrics within acceptable ranges
- [ ] Cost optimization verified
- [ ] Old App Engine services cleaned up

## 🔗 Useful Links

- [Cloud Run Documentation](https://cloud.google.com/run/docs)
- [App Engine to Cloud Run Migration Guide](https://cloud.google.com/appengine/migration-center/run/compare-gae-with-run)
- [Cloud Run Best Practices](https://cloud.google.com/run/docs/best-practices)
- [Cloud SQL with Cloud Run](https://cloud.google.com/sql/docs/mysql/connect-run)
